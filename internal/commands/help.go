package commands

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/bwmarrin/discordgo"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"
)

// HelpCommand 帮助命令实现
type HelpCommand struct {
	name        string
	description string
	category    string
	cooldown    time.Duration
	permissions []string
}

// NewHelpCommand 创建新的帮助命令
func NewHelpCommand() *HelpCommand {
	return &HelpCommand{
		name:        "help",
		description: "显示可用命令的帮助信息",
		category:    "工具",
		cooldown:    5 * time.Second,
		permissions: []string{},
	}
}

// GetName 获取命令名称
func (h *HelpCommand) GetName() string {
	return h.name
}

// GetDescription 获取命令描述
func (h *HelpCommand) GetDescription() string {
	return h.description
}

// GetCategory 获取命令分类
func (h *HelpCommand) GetCategory() string {
	return h.category
}

// GetCooldown 获取冷却时间
func (h *HelpCommand) GetCooldown() time.Duration {
	return h.cooldown
}

// GetPermissions 获取所需权限
func (h *HelpCommand) GetPermissions() []string {
	return h.permissions
}

// Validate 验证命令参数
func (h *HelpCommand) Validate(interaction *discordgo.InteractionCreate) error {
	// 帮助命令参数验证
	data := interaction.ApplicationCommandData()

	// 检查是否有命令名称参数
	if len(data.Options) > 0 {
		option := data.Options[0]
		if option.Type != discordgo.ApplicationCommandOptionString {
			return fmt.Errorf("命令名称必须是字符串")
		}
	}

	return nil
}

// Execute 执行命令
func (h *HelpCommand) Execute(ctx context.Context, client *types.Client, interaction *discordgo.InteractionCreate) error {
	var user *discordgo.User
	if interaction.Member != nil {
		user = interaction.Member.User
	} else {
		user = interaction.User
	}

	logger.Info("执行 Help 命令", "user", user.Username, "guild", interaction.GuildID)

	data := interaction.ApplicationCommandData()

	// 检查是否请求特定命令的帮助
	if len(data.Options) > 0 {
		commandName := data.Options[0].StringValue()
		return h.showSpecificCommandHelp(client, interaction, commandName, user)
	}

	// 显示所有命令的帮助
	return h.showAllCommandsHelp(client, interaction, user)
}

// showAllCommandsHelp 显示所有命令的帮助
func (h *HelpCommand) showAllCommandsHelp(client *types.Client, interaction *discordgo.InteractionCreate, user *discordgo.User) error {
	// 获取所有命令（这里需要从某个地方获取命令列表）
	// 由于我们还没有实现命令管理器，这里先硬编码一些示例
	commands := []types.Command{
		NewPingCommand(),
		NewHelpCommand(),
	}

	// 按分类组织命令
	categories := make(map[string][]types.Command)
	for _, cmd := range commands {
		category := cmd.GetCategory()
		if categories[category] == nil {
			categories[category] = make([]types.Command, 0)
		}
		categories[category] = append(categories[category], cmd)
	}

	// 创建嵌入消息
	embed := &discordgo.MessageEmbed{
		Title:       "📚 命令帮助",
		Description: "以下是所有可用的命令：",
		Color:       0x3498db, // 蓝色
		Footer: &discordgo.MessageEmbedFooter{
			Text: fmt.Sprintf("请求者: %s | 使用 /help <命令名> 获取详细信息", user.Username),
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	// 按分类添加字段
	var categoryNames []string
	for category := range categories {
		categoryNames = append(categoryNames, category)
	}
	sort.Strings(categoryNames)

	for _, category := range categoryNames {
		cmds := categories[category]

		var commandList []string
		for _, cmd := range cmds {
			commandList = append(commandList, fmt.Sprintf("`/%s` - %s", cmd.GetName(), cmd.GetDescription()))
		}

		embed.Fields = append(embed.Fields, &discordgo.MessageEmbedField{
			Name:   fmt.Sprintf("📁 %s", category),
			Value:  strings.Join(commandList, "\n"),
			Inline: false,
		})
	}

	// 添加统计信息
	embed.Fields = append(embed.Fields, &discordgo.MessageEmbedField{
		Name:   "📊 统计",
		Value:  fmt.Sprintf("总共 %d 个命令，%d 个分类", len(commands), len(categories)),
		Inline: true,
	})

	// 发送响应
	return client.Session.InteractionRespond(interaction.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
		},
	})
}

// showSpecificCommandHelp 显示特定命令的详细帮助
func (h *HelpCommand) showSpecificCommandHelp(client *types.Client, interaction *discordgo.InteractionCreate, commandName string, user *discordgo.User) error {
	// 查找命令（这里需要从命令管理器获取）
	var targetCommand types.Command

	// 硬编码的命令查找
	switch commandName {
	case "ping":
		targetCommand = NewPingCommand()
	case "help":
		targetCommand = NewHelpCommand()
	default:
		return client.Session.InteractionRespond(interaction.Interaction, &discordgo.InteractionResponse{
			Type: discordgo.InteractionResponseChannelMessageWithSource,
			Data: &discordgo.InteractionResponseData{
				Content: fmt.Sprintf("❌ 未找到命令 `%s`", commandName),
				Flags:   discordgo.MessageFlagsEphemeral,
			},
		})
	}

	// 创建详细的帮助嵌入
	embed := &discordgo.MessageEmbed{
		Title:       fmt.Sprintf("📖 命令详情: /%s", targetCommand.GetName()),
		Description: targetCommand.GetDescription(),
		Color:       0x2ecc71, // 绿色
		Footer: &discordgo.MessageEmbedFooter{
			Text: fmt.Sprintf("请求者: %s", user.Username),
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	// 添加详细信息字段
	embed.Fields = []*discordgo.MessageEmbedField{
		{
			Name:   "📂 分类",
			Value:  targetCommand.GetCategory(),
			Inline: true,
		},
		{
			Name:   "⏱️ 冷却时间",
			Value:  targetCommand.GetCooldown().String(),
			Inline: true,
		},
	}

	// 添加权限要求
	permissions := targetCommand.GetPermissions()
	if len(permissions) > 0 {
		embed.Fields = append(embed.Fields, &discordgo.MessageEmbedField{
			Name:   "🔒 所需权限",
			Value:  strings.Join(permissions, ", "),
			Inline: false,
		})
	} else {
		embed.Fields = append(embed.Fields, &discordgo.MessageEmbedField{
			Name:   "🔓 权限要求",
			Value:  "无特殊权限要求",
			Inline: false,
		})
	}

	// 发送响应
	return client.Session.InteractionRespond(interaction.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Embeds: []*discordgo.MessageEmbed{embed},
		},
	})
}

// GetApplicationCommand 获取应用命令定义
func (h *HelpCommand) GetApplicationCommand() *discordgo.ApplicationCommand {
	return &discordgo.ApplicationCommand{
		Name:        h.name,
		Description: h.description,
		Type:        discordgo.ChatApplicationCommand,
		Options: []*discordgo.ApplicationCommandOption{
			{
				Type:        discordgo.ApplicationCommandOptionString,
				Name:        "command",
				Description: "要查看详细信息的命令名称",
				Required:    false,
			},
		},
	}
}
