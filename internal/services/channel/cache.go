package channel

import (
	"context"
	"fmt"
	"sync"
	"time"

	"zeka-go/internal/services"
	"zeka-go/internal/services/logger"

	"github.com/bwmarrin/discordgo"
)

// ChannelNameCache 频道名称缓存服务
// 实现频道ID到频道名称的映射和缓存
type ChannelNameCache struct {
	// 服务基础信息
	name         string
	serviceType  string
	dependencies []string

	// Discord会话
	session *discordgo.Session

	// 缓存数据
	cache map[string]string // channelID -> channelName
	mu    sync.RWMutex

	// 缓存配置
	ttl         time.Duration
	maxSize     int
	lastCleanup time.Time

	// 运行状态
	isRunning bool
	ctx       context.Context
	cancel    context.CancelFunc
}

// NewChannelNameCache 创建频道名称缓存服务
func NewChannelNameCache(session *discordgo.Session) *ChannelNameCache {
	ctx, cancel := context.WithCancel(context.Background())

	return &ChannelNameCache{
		name:         "ChannelNameCache",
		serviceType:  "cache",
		dependencies: []string{}, // 无依赖
		session:      session,
		cache:        make(map[string]string),
		ttl:          30 * time.Minute, // 缓存30分钟
		maxSize:      1000,             // 最多缓存1000个频道
		lastCleanup:  time.Now(),
		ctx:          ctx,
		cancel:       cancel,
	}
}

// GetName 获取服务名称
func (cnc *ChannelNameCache) GetName() string {
	return cnc.name
}

// GetType 获取服务类型
func (cnc *ChannelNameCache) GetType() string {
	return cnc.serviceType
}

// GetDependencies 获取服务依赖
func (cnc *ChannelNameCache) GetDependencies() []string {
	return cnc.dependencies
}

// Initialize 初始化服务
func (cnc *ChannelNameCache) Initialize(ctx context.Context) error {
	// 频道名称缓存服务无需特殊初始化
	logger.Info("频道名称缓存服务初始化完成")
	return nil
}

// HealthCheck 健康检查
func (cnc *ChannelNameCache) HealthCheck(ctx context.Context) *services.HealthCheckResult {
	cnc.mu.RLock()
	defer cnc.mu.RUnlock()

	startTime := time.Now()

	if !cnc.isRunning {
		return &services.HealthCheckResult{
			Healthy:      false,
			Message:      "频道名称缓存服务未运行",
			CheckTime:    startTime,
			ResponseTime: time.Since(startTime),
		}
	}

	if cnc.session == nil {
		return &services.HealthCheckResult{
			Healthy:      false,
			Message:      "Discord会话未设置",
			CheckTime:    startTime,
			ResponseTime: time.Since(startTime),
		}
	}

	return &services.HealthCheckResult{
		Healthy:      true,
		Message:      fmt.Sprintf("频道缓存正常，已缓存 %d 个频道", len(cnc.cache)),
		CheckTime:    startTime,
		ResponseTime: time.Since(startTime),
	}
}

// Start 启动缓存服务
func (cnc *ChannelNameCache) Start(ctx context.Context) error {
	cnc.mu.Lock()
	defer cnc.mu.Unlock()

	if cnc.isRunning {
		return fmt.Errorf("频道名称缓存服务已在运行")
	}

	cnc.isRunning = true

	// 启动定期清理goroutine
	go cnc.cleanupRoutine()

	logger.Info("✅ 频道名称缓存服务已启动")
	return nil
}

// Stop 停止缓存服务
func (cnc *ChannelNameCache) Stop(ctx context.Context) error {
	cnc.mu.Lock()
	defer cnc.mu.Unlock()

	if !cnc.isRunning {
		return nil
	}

	cnc.cancel()
	cnc.isRunning = false

	logger.Info("频道名称缓存服务已停止")
	return nil
}

// GetChannelName 获取频道名称
func (cnc *ChannelNameCache) GetChannelName(channelID string) (string, error) {
	if channelID == "" {
		return "", fmt.Errorf("频道ID不能为空")
	}

	// 先尝试从缓存获取
	cnc.mu.RLock()
	if name, exists := cnc.cache[channelID]; exists {
		cnc.mu.RUnlock()
		return name, nil
	}
	cnc.mu.RUnlock()

	// 从Discord API获取
	channel, err := cnc.session.Channel(channelID)
	if err != nil {
		logger.Error("获取频道信息失败", "channel_id", channelID, "error", err)
		return "", fmt.Errorf("获取频道信息失败: %w", err)
	}

	channelName := channel.Name
	if channelName == "" {
		// 如果是DM频道或其他特殊频道，使用频道ID的后6位
		if len(channelID) > 6 {
			channelName = "ch_" + channelID[len(channelID)-6:]
		} else {
			channelName = "ch_" + channelID
		}
	}

	// 缓存结果
	cnc.mu.Lock()
	if len(cnc.cache) < cnc.maxSize {
		cnc.cache[channelID] = channelName
	}
	cnc.mu.Unlock()

	logger.Debug("频道名称已缓存", "channel_id", channelID, "channel_name", channelName)
	return channelName, nil
}

// GetChannelNames 批量获取频道名称
func (cnc *ChannelNameCache) GetChannelNames(channelIDs []string) (map[string]string, error) {
	result := make(map[string]string)

	for _, channelID := range channelIDs {
		name, err := cnc.GetChannelName(channelID)
		if err != nil {
			logger.Warn("获取频道名称失败", "channel_id", channelID, "error", err)
			// 使用频道ID作为fallback
			if len(channelID) > 6 {
				result[channelID] = "ch_" + channelID[len(channelID)-6:]
			} else {
				result[channelID] = "ch_" + channelID
			}
		} else {
			result[channelID] = name
		}
	}

	return result, nil
}

// GetChannelInfo 获取完整的频道信息（包括guild_id）
func (cnc *ChannelNameCache) GetChannelInfo(channelID string) (channelName, guildID string, err error) {
	if channelID == "" {
		return "", "", fmt.Errorf("频道ID不能为空")
	}

	// 从Discord API获取完整频道信息
	channel, err := cnc.session.Channel(channelID)
	if err != nil {
		logger.Error("获取频道信息失败", "channel_id", channelID, "error", err)
		return "", "", fmt.Errorf("获取频道信息失败: %w", err)
	}

	channelName = channel.Name
	if channelName == "" {
		// 如果是DM频道或其他特殊频道，使用频道ID的后6位
		if len(channelID) > 6 {
			channelName = "ch_" + channelID[len(channelID)-6:]
		} else {
			channelName = "ch_" + channelID
		}
	}

	guildID = channel.GuildID

	// 缓存频道名称
	cnc.mu.Lock()
	if len(cnc.cache) < cnc.maxSize {
		cnc.cache[channelID] = channelName
	}
	cnc.mu.Unlock()

	logger.Debug("频道信息已获取", "channel_id", channelID, "channel_name", channelName, "guild_id", guildID)
	return channelName, guildID, nil
}

// InvalidateCache 清除指定频道的缓存
func (cnc *ChannelNameCache) InvalidateCache(channelID string) {
	cnc.mu.Lock()
	defer cnc.mu.Unlock()

	delete(cnc.cache, channelID)
	logger.Debug("频道缓存已清除", "channel_id", channelID)
}

// ClearCache 清除所有缓存
func (cnc *ChannelNameCache) ClearCache() {
	cnc.mu.Lock()
	defer cnc.mu.Unlock()

	cnc.cache = make(map[string]string)
	logger.Info("所有频道缓存已清除")
}

// GetCacheStats 获取缓存统计信息
func (cnc *ChannelNameCache) GetCacheStats() map[string]interface{} {
	cnc.mu.RLock()
	defer cnc.mu.RUnlock()

	return map[string]interface{}{
		"cache_size":   len(cnc.cache),
		"max_size":     cnc.maxSize,
		"ttl_minutes":  int(cnc.ttl.Minutes()),
		"last_cleanup": cnc.lastCleanup,
		"is_running":   cnc.isRunning,
	}
}

// cleanupRoutine 定期清理过期缓存
func (cnc *ChannelNameCache) cleanupRoutine() {
	ticker := time.NewTicker(10 * time.Minute) // 每10分钟清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			cnc.performCleanup()
		case <-cnc.ctx.Done():
			return
		}
	}
}

// performCleanup 执行缓存清理
func (cnc *ChannelNameCache) performCleanup() {
	cnc.mu.Lock()
	defer cnc.mu.Unlock()

	// 如果缓存大小超过限制，清理一半
	if len(cnc.cache) > cnc.maxSize {
		newCache := make(map[string]string)
		count := 0
		maxKeep := cnc.maxSize / 2

		for k, v := range cnc.cache {
			if count < maxKeep {
				newCache[k] = v
				count++
			}
		}

		cnc.cache = newCache
		cnc.lastCleanup = time.Now()

		logger.Info("缓存清理完成",
			"old_size", len(cnc.cache),
			"new_size", len(newCache),
			"max_size", cnc.maxSize)
	}
}

// GenerateRuleName 生成基于频道名称的规则名称（遵循设计文档）
func (cnc *ChannelNameCache) GenerateRuleName(sourceChannelID, targetChannelID string) (string, error) {
	// 获取频道名称
	sourceNames, err := cnc.GetChannelNames([]string{sourceChannelID, targetChannelID})
	if err != nil {
		return "", fmt.Errorf("获取频道名称失败: %w", err)
	}

	sourceName := sourceNames[sourceChannelID]
	targetName := sourceNames[targetChannelID]

	// 清理频道名称（移除特殊字符）
	sourceName = cleanChannelName(sourceName)
	targetName = cleanChannelName(targetName)

	// 生成规则名称：{inputChannelName}_to_{outputChannelName}
	ruleName := fmt.Sprintf("%s_to_%s", sourceName, targetName)

	logger.Debug("生成规则名称",
		"source_id", sourceChannelID,
		"target_id", targetChannelID,
		"source_name", sourceName,
		"target_name", targetName,
		"rule_name", ruleName)

	return ruleName, nil
}

// cleanChannelName 清理频道名称，移除特殊字符
func cleanChannelName(name string) string {
	// 移除特殊字符，只保留字母、数字、下划线和连字符
	result := ""
	for _, r := range name {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') ||
			(r >= '0' && r <= '9') || r == '_' || r == '-' {
			result += string(r)
		}
	}

	// 如果结果为空，使用默认名称
	if result == "" {
		result = "channel"
	}

	// 限制长度
	if len(result) > 20 {
		result = result[:20]
	}

	return result
}
