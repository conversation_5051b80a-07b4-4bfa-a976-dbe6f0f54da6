package filter

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"zeka-go/internal/services"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"gopkg.in/yaml.v3"
)

// ChannelCacheInterface 频道缓存接口
type ChannelCacheInterface interface {
	GetChannelName(channelID string) (string, error)
	GetChannelInfo(channelID string) (channelName, guildID string, err error)
}

// RedisFilterRule Redis存储的精简过滤规则结构（节省内存）
// 注意：不包含channel字段，因为频道信息已在channel_info中
type RedisFilterRule struct {
	Keyword   string `json:"keyword,omitempty"`    // 统一关键字
	Mode      string `json:"mode"`                 // whitelist, blacklist
	CreatedBy string `json:"created_by,omitempty"` // 创建者用户ID
}

// RedisFilterRulesConfig Redis存储的精简配置结构
type RedisFilterRulesConfig struct {
	ChannelInfo types.ChannelInfo `json:"channel_info"`
	FilterRules []RedisFilterRule `json:"filter_rules"`
}

// FilterRuleService 过滤规则服务（严格遵循设计文档）
type FilterRuleService struct {
	// 服务基础信息
	name         string
	serviceType  string
	dependencies []string

	// 配置和状态
	configDir string
	rules     map[string]*types.FilterRule // uniqueID -> rule (Channel+Keyword+Mode)
	mu        sync.RWMutex

	// 运行状态
	isRunning bool
	ctx       context.Context
	cancel    context.CancelFunc

	// 索引缓存（按频道组织）
	channelIndex map[string][]*types.FilterRule // channelID -> rules

	// 统计和监控
	channelStats map[string]*types.ChannelFilterStats
	lastLoaded   time.Time

	// 全局设置
	globalSettings *types.GlobalFilterSettings

	// 频道信息缓存
	channelInfos map[string]*types.ChannelInfo // channelID -> info

	// Redis缓存服务（可选）
	cacheService types.CacheService

	// 频道缓存服务（可选）
	channelCache ChannelCacheInterface
}

// NewFilterRuleService 创建过滤规则服务
func NewFilterRuleService(configDir string) *FilterRuleService {
	ctx, cancel := context.WithCancel(context.Background())

	return &FilterRuleService{
		name:         "FilterRuleService",
		serviceType:  "filter",
		dependencies: []string{"ForwardRuleService"},
		configDir:    configDir,
		rules:        make(map[string]*types.FilterRule),
		channelIndex: make(map[string][]*types.FilterRule),
		channelStats: make(map[string]*types.ChannelFilterStats),
		channelInfos: make(map[string]*types.ChannelInfo),
		ctx:          ctx,
		cancel:       cancel,
		globalSettings: &types.GlobalFilterSettings{
			DefaultAction:        "allow",
			DefaultLogicalOp:     "AND",
			CaseSensitiveDefault: false,
			MaxRulesPerChannel:   100,
			CacheEnabled:         true,
			CacheTTL:             10 * time.Minute,
		},
	}
}

// Initialize 初始化服务
func (frs *FilterRuleService) Initialize(ctx context.Context) error {
	logger.Info("初始化过滤规则服务", "config_dir", frs.configDir)

	// 确保配置目录存在
	if err := os.MkdirAll(frs.configDir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 加载所有配置文件
	if err := frs.loadAllConfigs(); err != nil {
		return fmt.Errorf("加载配置文件失败: %w", err)
	}

	// 构建索引
	frs.buildIndexes()

	logger.Info("过滤规则服务初始化完成",
		"rules_count", len(frs.rules),
		"channels", len(frs.channelIndex))

	return nil
}

// Start 启动服务
func (frs *FilterRuleService) Start(ctx context.Context) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if frs.isRunning {
		return fmt.Errorf("过滤规则服务已在运行")
	}

	// 初始化服务（加载配置文件）
	if err := frs.Initialize(ctx); err != nil {
		return fmt.Errorf("初始化服务失败: %w", err)
	}

	frs.isRunning = true
	logger.Info("过滤规则服务已启动")

	// 如果缓存服务已注入，进行全量同步
	if frs.cacheService != nil && len(frs.rules) > 0 {
		logger.Info("服务启动后开始全量同步到Redis")
		frs.syncAllToRedis()
	}

	return nil
}

// Stop 停止服务
func (frs *FilterRuleService) Stop(ctx context.Context) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if !frs.isRunning {
		return nil
	}

	frs.cancel()
	frs.isRunning = false

	logger.Info("过滤规则服务已停止")
	return nil
}

// HealthCheck 健康检查
func (frs *FilterRuleService) HealthCheck(ctx context.Context) *services.HealthCheckResult {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	startTime := time.Now()

	if !frs.isRunning {
		return &services.HealthCheckResult{
			Healthy:      false,
			Message:      "服务未运行",
			CheckTime:    startTime,
			ResponseTime: time.Since(startTime),
		}
	}

	if _, err := os.Stat(frs.configDir); os.IsNotExist(err) {
		return &services.HealthCheckResult{
			Healthy:      false,
			Message:      fmt.Sprintf("配置目录不存在: %s", frs.configDir),
			CheckTime:    startTime,
			ResponseTime: time.Since(startTime),
		}
	}

	// 检查Redis连接状态
	redisStatus := "未启用"
	if frs.cacheService != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()
		if err := frs.cacheService.Ping(ctx); err != nil {
			redisStatus = fmt.Sprintf("连接失败: %v", err)
		} else {
			redisStatus = "正常"
		}
	}

	message := fmt.Sprintf("服务正常，已加载 %d 个过滤规则，覆盖 %d 个频道，Redis状态: %s",
		len(frs.rules), len(frs.channelIndex), redisStatus)

	return &services.HealthCheckResult{
		Healthy:      true,
		Message:      message,
		CheckTime:    startTime,
		ResponseTime: time.Since(startTime),
	}
}

// GetName 获取服务名称
func (frs *FilterRuleService) GetName() string {
	return frs.name
}

// GetType 获取服务类型
func (frs *FilterRuleService) GetType() string {
	return frs.serviceType
}

// GetDependencies 获取服务依赖
func (frs *FilterRuleService) GetDependencies() []string {
	return frs.dependencies
}

// SetCacheService 设置缓存服务（依赖注入）
func (frs *FilterRuleService) SetCacheService(cacheService types.CacheService) {
	frs.cacheService = cacheService
	logger.Info("缓存服务已注入到过滤规则服务", "is_running", frs.isRunning, "rules_count", len(frs.rules))

	// 缓存服务注入后，立即进行全量同步
	if frs.isRunning && len(frs.rules) > 0 {
		logger.Info("缓存服务注入完成，开始全量同步到Redis")
		frs.syncAllToRedis()
	} else {
		logger.Info("跳过Redis同步", "is_running", frs.isRunning, "rules_count", len(frs.rules))
	}
}

// SetChannelCache 设置频道缓存服务（依赖注入）
func (frs *FilterRuleService) SetChannelCache(channelCache ChannelCacheInterface) {
	frs.channelCache = channelCache
	logger.Debug("频道缓存服务已注入到过滤规则服务")
}

// AddRule 添加过滤规则
func (frs *FilterRuleService) AddRule(rule *types.FilterRule) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if !frs.isRunning {
		return fmt.Errorf("过滤规则服务未运行")
	}

	// 验证规则
	if err := rule.Validate(); err != nil {
		return fmt.Errorf("规则验证失败: %w", err)
	}

	// 生成唯一标识符
	uniqueID := rule.GetUniqueID()

	// 检查规则是否已存在
	if _, exists := frs.rules[uniqueID]; exists {
		return fmt.Errorf("规则 %s 已存在", uniqueID)
	}

	// 检查频道规则数量限制
	channelRules := frs.channelIndex[rule.Channel]
	if len(channelRules) >= frs.globalSettings.MaxRulesPerChannel {
		return fmt.Errorf("频道 %s 的规则数量已达到上限 %d", rule.Channel, frs.globalSettings.MaxRulesPerChannel)
	}

	// 添加规则
	frs.rules[uniqueID] = rule

	// 重建索引
	frs.buildIndexes()

	// 保存配置到YAML文件
	if err := frs.saveChannelConfig(rule.Channel); err != nil {
		logger.Error("保存配置文件失败", "channel", rule.Channel, "error", err)
		// 不返回错误，因为规则已经添加到内存中
	}

	logger.Info("过滤规则已添加", "unique_id", uniqueID, "channel", rule.Channel, "keyword", rule.Keyword, "mode", rule.Mode)
	return nil
}

// RemoveRule 移除过滤规则
func (frs *FilterRuleService) RemoveRule(uniqueID string) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if !frs.isRunning {
		return fmt.Errorf("过滤规则服务未运行")
	}

	rule, exists := frs.rules[uniqueID]
	if !exists {
		return fmt.Errorf("规则 %s 不存在", uniqueID)
	}

	channelID := rule.Channel
	delete(frs.rules, uniqueID)

	// 重建索引
	frs.buildIndexes()

	// 保存配置到YAML文件
	if err := frs.saveChannelConfig(channelID); err != nil {
		logger.Error("保存配置文件失败", "channel", channelID, "error", err)
		// 不返回错误，因为规则已经从内存中删除
	}

	logger.Info("过滤规则已移除", "unique_id", uniqueID, "channel", channelID, "keyword", rule.Keyword)
	return nil
}

// UpdateRule 更新过滤规则
func (frs *FilterRuleService) UpdateRule(rule *types.FilterRule) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if !frs.isRunning {
		return fmt.Errorf("过滤规则服务未运行")
	}

	// 验证规则
	if err := rule.Validate(); err != nil {
		return fmt.Errorf("规则验证失败: %w", err)
	}

	uniqueID := rule.GetUniqueID()

	// 检查规则是否存在
	_, exists := frs.rules[uniqueID]
	if !exists {
		return fmt.Errorf("规则 %s 不存在", uniqueID)
	}

	// 更新规则
	frs.rules[uniqueID] = rule

	// 重建索引
	frs.buildIndexes()

	// 保存配置到YAML文件并同步到Redis
	if err := frs.saveChannelConfig(rule.Channel); err != nil {
		logger.Error("保存配置文件失败", "channel", rule.Channel, "error", err)
		// 不返回错误，因为规则已经更新到内存中
	}

	logger.Info("过滤规则已更新", "unique_id", uniqueID)
	return nil
}

// GetRule 获取过滤规则
func (frs *FilterRuleService) GetRule(uniqueID string) (*types.FilterRule, error) {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	rule, exists := frs.rules[uniqueID]
	if !exists {
		return nil, fmt.Errorf("规则 %s 不存在", uniqueID)
	}

	// 返回副本
	ruleCopy := *rule
	return &ruleCopy, nil
}

// ListRules 列出指定频道的过滤规则
func (frs *FilterRuleService) ListRules(channelID string) []*types.FilterRule {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	rules := frs.channelIndex[channelID]
	if rules == nil {
		return []*types.FilterRule{}
	}

	// 返回副本
	result := make([]*types.FilterRule, len(rules))
	for i, rule := range rules {
		ruleCopy := *rule
		result[i] = &ruleCopy
	}

	return result
}

// CheckMessage 检查消息是否通过过滤
func (frs *FilterRuleService) CheckMessage(channelID string, message interface{}) (*types.FilterResult, error) {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	startTime := time.Now()

	result := &types.FilterResult{
		Allowed:       true,
		Action:        frs.globalSettings.DefaultAction,
		MatchedRules:  []string{},
		CheckTime:     startTime,
		ProcessTimeMs: 0,
	}

	// 获取频道的过滤规则
	rules := frs.channelIndex[channelID]
	if len(rules) == 0 {
		result.ProcessTimeMs = float64(time.Since(startTime).Nanoseconds()) / 1e6
		return result, nil
	}

	// 转换消息为字符串内容
	content := fmt.Sprintf("%v", message)

	// 应用过滤规则
	for _, rule := range rules {
		if rule.MatchesContent(content) {
			result.MatchedRules = append(result.MatchedRules, rule.GetUniqueID())

			// 根据规则模式更新结果
			if rule.Mode == "blacklist" {
				result.Allowed = false
				result.Action = "deny"
				result.Reason = fmt.Sprintf("被黑名单规则拒绝: %s", rule.Keyword)
				break // 黑名单匹配立即拒绝
			} else if rule.Mode == "whitelist" {
				result.Allowed = true
				result.Action = "allow"
				result.Reason = fmt.Sprintf("被白名单规则允许: %s", rule.Keyword)
			}
		}
	}

	result.ProcessTimeMs = float64(time.Since(startTime).Nanoseconds()) / 1e6
	return result, nil
}

// CheckProduct 检查产品是否通过过滤
func (frs *FilterRuleService) CheckProduct(channelID string, product *types.ProductItem) (*types.FilterResult, error) {
	// 将产品转换为搜索内容
	description := product.Description
	content := fmt.Sprintf("%s %s %s %s", product.Title, product.ProductID, product.URL, description)
	return frs.CheckMessage(channelID, content)
}

// 私有方法

// loadAllConfigs 加载所有配置文件（遵循设计文档的文件命名格式）
func (frs *FilterRuleService) loadAllConfigs() error {
	// 扫描配置目录，查找 filter_rules_channel_*.yaml 文件
	pattern := filepath.Join(frs.configDir, "filter_rules_channel_*.yaml")
	files, err := filepath.Glob(pattern)
	if err != nil {
		return fmt.Errorf("扫描配置文件失败: %w", err)
	}

	for _, file := range files {
		if err := frs.loadConfigFile(file); err != nil {
			logger.Error("加载配置文件失败", "file", file, "error", err)
			continue
		}
	}

	frs.lastLoaded = time.Now()
	return nil
}

// loadConfigFile 加载单个配置文件
func (frs *FilterRuleService) loadConfigFile(filename string) error {
	data, err := os.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	config := &types.FilterRulesConfig{}
	if err := yaml.Unmarshal(data, config); err != nil {
		return fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 从文件名中提取频道ID
	baseName := filepath.Base(filename)
	channelID := extractChannelIDFromFilename(baseName)
	if channelID == "" {
		return fmt.Errorf("无法从文件名提取频道ID: %s", filename)
	}

	// 确保频道信息中的频道ID正确
	if config.ChannelInfo.ChannelID == "" {
		config.ChannelInfo.ChannelID = channelID
	}

	// 缓存频道信息
	frs.channelInfos[config.ChannelInfo.ChannelID] = &config.ChannelInfo

	// 加载规则并设置频道ID
	for i := range config.FilterRules {
		rule := &config.FilterRules[i]

		// 从文件名设置频道ID（优先级高于YAML中的值）
		rule.Channel = channelID

		if err := rule.Validate(); err != nil {
			logger.Error("过滤规则验证失败", "rule", rule.GetUniqueID(), "error", err)
			continue
		}

		uniqueID := rule.GetUniqueID()
		frs.rules[uniqueID] = rule
	}

	return nil
}

// buildIndexes 构建索引
func (frs *FilterRuleService) buildIndexes() {
	frs.channelIndex = make(map[string][]*types.FilterRule)

	for _, rule := range frs.rules {
		// 所有规则都索引（添加的规则都是启用的）
		frs.channelIndex[rule.Channel] = append(frs.channelIndex[rule.Channel], rule)
	}
}

// saveChannelConfig 保存指定频道的配置到YAML文件
func (frs *FilterRuleService) saveChannelConfig(channelID string) error {
	// 获取频道的所有规则
	rules := frs.channelIndex[channelID]
	if len(rules) == 0 {
		// 如果没有规则，删除配置文件
		filename := filepath.Join(frs.configDir, fmt.Sprintf("filter_rules_channel_%s.yaml", channelID))
		if err := os.Remove(filename); err != nil && !os.IsNotExist(err) {
			return fmt.Errorf("删除配置文件失败: %w", err)
		}
		return nil
	}

	// 创建配置结构
	config := &types.FilterRulesConfig{
		ChannelInfo: types.ChannelInfo{
			ChannelID: channelID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		FilterRules: make([]types.FilterRule, len(rules)),
	}

	// 如果有缓存的频道信息，使用缓存的信息
	if cachedInfo, exists := frs.channelInfos[channelID]; exists {
		config.ChannelInfo = *cachedInfo
		config.ChannelInfo.UpdatedAt = time.Now()
	}

	// 复制规则
	for i, rule := range rules {
		config.FilterRules[i] = *rule
	}

	// 序列化为YAML
	data, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	// 写入文件
	filename := filepath.Join(frs.configDir, fmt.Sprintf("filter_rules_channel_%s.yaml", channelID))
	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	logger.Info("配置文件已保存", "file", filename, "rules_count", len(rules))

	// 同步到Redis
	frs.syncToRedis(channelID)

	return nil
}

// extractChannelIDFromFilename 从文件名中提取频道ID
// 文件名格式：filter_rules_channel_1396024418542686229.yaml
func extractChannelIDFromFilename(filename string) string {
	// 移除文件扩展名
	name := strings.TrimSuffix(filename, ".yaml")
	name = strings.TrimSuffix(name, ".yml")

	// 检查是否符合预期格式
	prefix := "filter_rules_channel_"
	if !strings.HasPrefix(name, prefix) {
		return ""
	}

	// 提取频道ID
	channelID := strings.TrimPrefix(name, prefix)
	if channelID == "" {
		return ""
	}

	return channelID
}

// enrichChannelInfo 丰富频道信息（获取频道名称和服务器ID）
func (frs *FilterRuleService) enrichChannelInfo(channelID string, info *types.ChannelInfo) {
	// 如果频道缓存服务可用，尝试获取完整的频道信息
	if frs.channelCache != nil {
		// 如果频道名称或服务器ID为空，尝试获取完整信息
		if info.ChannelName == "" || info.GuildID == "" {
			if channelName, guildID, err := frs.channelCache.GetChannelInfo(channelID); err == nil {
				if info.ChannelName == "" {
					info.ChannelName = channelName
				}
				if info.GuildID == "" {
					info.GuildID = guildID
				}
				logger.Debug("已获取频道信息", "channel_id", channelID, "channel_name", channelName, "guild_id", guildID)
			} else {
				logger.Debug("获取频道信息失败", "channel_id", channelID, "error", err)
				// 降级：只尝试获取频道名称
				if info.ChannelName == "" {
					if channelName, err := frs.channelCache.GetChannelName(channelID); err == nil {
						info.ChannelName = channelName
						logger.Debug("已获取频道名称", "channel_id", channelID, "channel_name", channelName)
					}
				}
			}
		}
	}
}

// syncToRedis 同步频道配置到Redis（异步执行）
func (frs *FilterRuleService) syncToRedis(channelID string) {
	if frs.cacheService == nil {
		return // Redis服务未启用，跳过同步
	}

	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// 构建Redis键名
		redisKey := fmt.Sprintf("filter_rules:channel:%s", channelID)

		// 获取频道的所有规则
		rules := frs.channelIndex[channelID]
		if len(rules) == 0 {
			// 如果没有规则，删除Redis中的键
			if err := frs.cacheService.Del(ctx, redisKey); err != nil {
				logger.Warn("删除Redis配置失败", "channel", channelID, "error", err)
			} else {
				logger.Debug("Redis配置已删除", "channel", channelID)
			}
			return
		}

		// 创建精简的Redis配置结构（节省内存）
		config := &RedisFilterRulesConfig{
			ChannelInfo: types.ChannelInfo{
				ChannelID: channelID,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			FilterRules: make([]RedisFilterRule, len(rules)),
		}

		// 如果有缓存的频道信息，使用缓存的信息
		if cachedInfo, exists := frs.channelInfos[channelID]; exists {
			config.ChannelInfo = *cachedInfo
			config.ChannelInfo.UpdatedAt = time.Now()
		}

		// 丰富频道信息（获取频道名称等）
		frs.enrichChannelInfo(channelID, &config.ChannelInfo)

		// 复制规则（只保留必要字段，节省Redis内存）
		// 注意：不包含channel字段，因为频道信息已在channel_info中
		for i, rule := range rules {
			config.FilterRules[i] = RedisFilterRule{
				Keyword:   rule.Keyword,
				Mode:      rule.Mode,
				CreatedBy: rule.CreatedBy,
			}
		}

		// 序列化配置为JSON
		configData, err := json.Marshal(config)
		if err != nil {
			logger.Warn("序列化配置失败", "channel", channelID, "error", err)
			return
		}

		// 存储到Redis，永久缓存
		if err := frs.cacheService.Set(ctx, redisKey, string(configData), 0); err != nil {
			logger.Warn("同步配置到Redis失败", "channel", channelID, "error", err)
		} else {
			logger.Debug("配置已同步到Redis", "channel", channelID, "rules_count", len(rules), "updated_at", config.ChannelInfo.UpdatedAt)
		}
	}()
}

// syncAllToRedis 全量同步所有频道配置到Redis
func (frs *FilterRuleService) syncAllToRedis() {
	if frs.cacheService == nil {
		return // Redis服务未启用，跳过同步
	}

	logger.Info("开始全量同步过滤规则到Redis", "channels", len(frs.channelIndex))

	for channelID := range frs.channelIndex {
		frs.syncToRedis(channelID)
	}

	logger.Info("全量同步完成")
}
