package discord

import (
	"context"
	"fmt"
	"sync"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// 上下文键类型
type contextKey string

const (
	ContextKeyInteractionID contextKey = "interaction_id"
	ContextKeyUserID        contextKey = "user_id"
	ContextKeyGuildID       contextKey = "guild_id"
	ContextKeyChannelID     contextKey = "channel_id"
)

// Client Discord 客户端封装
type Client struct {
	session *discordgo.Session
	config  *types.Config

	// 事件处理
	eventHandlers map[string][]types.EventHandler
	eventMu       sync.RWMutex

	// 命令管理
	commands  map[string]types.Command
	commandMu sync.RWMutex

	// 组件管理
	components  map[string]types.ComponentHandler
	componentMu sync.RWMutex

	// 中间件
	middlewares  []types.Middleware
	middlewareMu sync.RWMutex

	// 状态管理
	isConnected bool
	connMu      sync.RWMutex

	// 重连管理
	reconnectAttempts    int
	maxReconnectAttempts int
	reconnectDelay       time.Duration

	// 上下文
	ctx    context.Context
	cancel context.CancelFunc
}

// NewClient 创建新的 Discord 客户端
func NewClient(config *types.Config) (*Client, error) {
	if config == nil {
		return nil, types.ErrInvalidConfig
	}

	// 创建 Discord 会话
	session, err := discordgo.New("Bot " + config.Discord.Token)
	if err != nil {
		return nil, fmt.Errorf("创建 Discord 会话失败: %w", err)
	}

	// 设置 Intent
	session.Identify.Intents = discordgo.IntentsGuilds |
		discordgo.IntentsGuildMessages |
		discordgo.IntentsGuildMembers |
		discordgo.IntentsMessageContent |
		discordgo.IntentsGuildVoiceStates |
		discordgo.IntentsGuildInvites |
		discordgo.IntentsGuildMessageReactions

	client := &Client{
		session:              session,
		config:               config,
		eventHandlers:        make(map[string][]types.EventHandler),
		commands:             make(map[string]types.Command),
		components:           make(map[string]types.ComponentHandler),
		middlewares:          make([]types.Middleware, 0),
		maxReconnectAttempts: 10,
		reconnectDelay:       5 * time.Second,
	}

	// 设置基础事件处理器
	client.setupBaseHandlers()

	return client, nil
}

// setupBaseHandlers 设置基础事件处理器
func (c *Client) setupBaseHandlers() {
	// Ready 事件
	c.session.AddHandler(func(s *discordgo.Session, r *discordgo.Ready) {
		c.connMu.Lock()
		c.isConnected = true
		c.reconnectAttempts = 0
		c.connMu.Unlock()

		logger.Info("Discord 客户端已连接",
			"user", r.User.Username+"#"+r.User.Discriminator,
			"guilds", len(r.Guilds),
			"session_id", r.SessionID)

		// 设置状态
		c.setStatus()

		// 触发自定义 Ready 事件处理器
		c.handleEvent("ready", r)
	})

	// Disconnect 事件
	c.session.AddHandler(func(s *discordgo.Session, d *discordgo.Disconnect) {
		c.connMu.Lock()
		c.isConnected = false
		c.connMu.Unlock()

		logger.Warn("Discord 连接断开")

		// 触发自定义 Disconnect 事件处理器
		c.handleEvent("disconnect", d)

		// 尝试重连
		go c.attemptReconnect()
	})

	// 交互事件处理已移至Bot级别，避免重复处理
	// c.session.AddHandler(func(s *discordgo.Session, i *discordgo.InteractionCreate) {
	//	c.handleInteraction(s, i)
	// })

	// 消息事件
	c.session.AddHandler(func(s *discordgo.Session, m *discordgo.MessageCreate) {
		c.handleEvent("messageCreate", m)
	})

	// 服务器事件
	c.session.AddHandler(func(s *discordgo.Session, g *discordgo.GuildCreate) {
		logger.Info("加入服务器", "guild", g.Name, "id", g.ID, "members", g.MemberCount)
		c.handleEvent("guildCreate", g)
	})

	c.session.AddHandler(func(s *discordgo.Session, g *discordgo.GuildDelete) {
		logger.Info("离开服务器", "guild", g.Name, "id", g.ID)
		c.handleEvent("guildDelete", g)
	})

	// 成员事件
	c.session.AddHandler(func(s *discordgo.Session, m *discordgo.GuildMemberAdd) {
		c.handleEvent("guildMemberAdd", m)
	})

	c.session.AddHandler(func(s *discordgo.Session, m *discordgo.GuildMemberRemove) {
		c.handleEvent("guildMemberRemove", m)
	})

	// 反应事件
	c.session.AddHandler(func(s *discordgo.Session, r *discordgo.MessageReactionAdd) {
		c.handleEvent("messageReactionAdd", r)
	})

	c.session.AddHandler(func(s *discordgo.Session, r *discordgo.MessageReactionRemove) {
		c.handleEvent("messageReactionRemove", r)
	})
}

// Connect 连接到 Discord
func (c *Client) Connect(ctx context.Context) error {
	c.ctx, c.cancel = context.WithCancel(ctx)

	logger.Info("正在连接到 Discord...")

	if err := c.session.Open(); err != nil {
		return fmt.Errorf("连接 Discord 失败: %w", err)
	}

	// 等待连接建立
	timeout := time.After(30 * time.Second)
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return fmt.Errorf("连接超时")
		case <-ticker.C:
			c.connMu.RLock()
			connected := c.isConnected
			c.connMu.RUnlock()

			if connected {
				logger.Info("Discord 连接建立成功")
				return nil
			}
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}

// Disconnect 断开连接
func (c *Client) Disconnect() error {
	logger.Info("正在断开 Discord 连接...")

	if c.cancel != nil {
		c.cancel()
	}

	c.connMu.Lock()
	c.isConnected = false
	c.connMu.Unlock()

	if err := c.session.Close(); err != nil {
		return fmt.Errorf("断开连接失败: %w", err)
	}

	logger.Info("Discord 连接已断开")
	return nil
}

// IsConnected 检查是否已连接
func (c *Client) IsConnected() bool {
	c.connMu.RLock()
	defer c.connMu.RUnlock()
	return c.isConnected
}

// attemptReconnect 尝试重连
func (c *Client) attemptReconnect() {
	c.connMu.Lock()
	if c.reconnectAttempts >= c.maxReconnectAttempts {
		c.connMu.Unlock()
		logger.Error("达到最大重连次数，停止重连", "attempts", c.reconnectAttempts)
		return
	}

	c.reconnectAttempts++
	attempts := c.reconnectAttempts
	c.connMu.Unlock()

	logger.Info("尝试重连", "attempt", attempts, "max", c.maxReconnectAttempts)

	// 等待重连延迟
	select {
	case <-time.After(c.reconnectDelay):
	case <-c.ctx.Done():
		return
	}

	// 尝试重新连接
	if err := c.session.Open(); err != nil {
		logger.Error("重连失败", "error", err, "attempt", attempts)
		// 递归尝试下一次重连
		go c.attemptReconnect()
	}
}

// setStatus 设置 Bot 状态
func (c *Client) setStatus() {
	if len(c.config.StatusMessages) == 0 {
		return
	}

	// 使用第一个状态消息
	status := c.config.StatusMessages[0]

	var activityType discordgo.ActivityType
	switch status.Type {
	case "Playing":
		activityType = discordgo.ActivityTypeGame
	case "Listening":
		activityType = discordgo.ActivityTypeListening
	case "Watching":
		activityType = discordgo.ActivityTypeWatching
	case "Competing":
		activityType = discordgo.ActivityTypeCompeting
	default:
		activityType = discordgo.ActivityTypeGame
	}

	err := c.session.UpdateStatusComplex(discordgo.UpdateStatusData{
		Activities: []*discordgo.Activity{
			{
				Name: status.Content,
				Type: activityType,
			},
		},
		Status: "online",
	})

	if err != nil {
		logger.Error("设置状态失败", "error", err)
	} else {
		logger.Info("状态设置成功", "type", status.Type, "content", status.Content)
	}
}

// RegisterEventHandler 注册事件处理器
func (c *Client) RegisterEventHandler(eventType string, handler types.EventHandler) {
	c.eventMu.Lock()
	defer c.eventMu.Unlock()

	if c.eventHandlers[eventType] == nil {
		c.eventHandlers[eventType] = make([]types.EventHandler, 0)
	}

	c.eventHandlers[eventType] = append(c.eventHandlers[eventType], handler)
	logger.Debug("注册事件处理器", "event", eventType, "handler", fmt.Sprintf("%T", handler))
}

// UnregisterEventHandler 注销事件处理器
func (c *Client) UnregisterEventHandler(eventType string, handler types.EventHandler) {
	c.eventMu.Lock()
	defer c.eventMu.Unlock()

	handlers := c.eventHandlers[eventType]
	for i, h := range handlers {
		if h == handler {
			c.eventHandlers[eventType] = append(handlers[:i], handlers[i+1:]...)
			logger.Debug("注销事件处理器", "event", eventType, "handler", fmt.Sprintf("%T", handler))
			break
		}
	}
}

// handleEvent 处理事件
func (c *Client) handleEvent(eventType string, event interface{}) {
	c.eventMu.RLock()
	handlers := c.eventHandlers[eventType]
	c.eventMu.RUnlock()

	if len(handlers) == 0 {
		return
	}

	// 创建上下文
	ctx := c.ctx
	if ctx == nil {
		ctx = context.Background()
	}

	// 并发处理所有处理器
	for _, handler := range handlers {
		go func(h types.EventHandler) {
			defer func() {
				if r := recover(); r != nil {
					logger.Error("事件处理器 panic", "event", eventType, "handler", fmt.Sprintf("%T", h), "panic", r)
				}
			}()

			if !h.ShouldHandle(event) {
				return
			}

			client := types.NewClient(c.session, c.config)
			if err := h.Handle(ctx, client, event); err != nil {
				logger.Error("事件处理失败", "event", eventType, "handler", fmt.Sprintf("%T", h), "error", err)
			}
		}(handler)
	}
}

// GetSession 获取 Discord 会话
func (c *Client) GetSession() *discordgo.Session {
	return c.session
}

// GetConfig 获取配置
func (c *Client) GetConfig() *types.Config {
	return c.config
}

// RegisterCommand 注册命令
func (c *Client) RegisterCommand(command types.Command) error {
	c.commandMu.Lock()
	defer c.commandMu.Unlock()

	name := command.GetName()
	if _, exists := c.commands[name]; exists {
		return fmt.Errorf("命令 %s 已存在", name)
	}

	c.commands[name] = command
	logger.Debug("注册命令", "name", name, "description", command.GetDescription())
	return nil
}

// UnregisterCommand 注销命令
func (c *Client) UnregisterCommand(name string) {
	c.commandMu.Lock()
	defer c.commandMu.Unlock()

	delete(c.commands, name)
	logger.Debug("注销命令", "name", name)
}

// GetCommand 获取命令
func (c *Client) GetCommand(name string) (types.Command, bool) {
	c.commandMu.RLock()
	defer c.commandMu.RUnlock()

	command, exists := c.commands[name]
	return command, exists
}

// RegisterComponent 注册组件处理器
func (c *Client) RegisterComponent(component types.ComponentHandler) error {
	c.componentMu.Lock()
	defer c.componentMu.Unlock()

	customID := component.GetCustomID()
	if _, exists := c.components[customID]; exists {
		return fmt.Errorf("组件 %s 已存在", customID)
	}

	c.components[customID] = component
	logger.Debug("注册组件", "custom_id", customID, "type", component.GetType())
	return nil
}

// UnregisterComponent 注销组件处理器
func (c *Client) UnregisterComponent(customID string) {
	c.componentMu.Lock()
	defer c.componentMu.Unlock()

	delete(c.components, customID)
	logger.Debug("注销组件", "custom_id", customID)
}

// GetComponent 获取组件处理器
func (c *Client) GetComponent(customID string) (types.ComponentHandler, bool) {
	c.componentMu.RLock()
	defer c.componentMu.RUnlock()

	component, exists := c.components[customID]
	return component, exists
}

// AddMiddleware 添加中间件
func (c *Client) AddMiddleware(middleware types.Middleware) {
	c.middlewareMu.Lock()
	defer c.middlewareMu.Unlock()

	c.middlewares = append(c.middlewares, middleware)
	logger.Debug("添加中间件", "middleware", fmt.Sprintf("%T", middleware))
}

// handleInteraction 处理交互事件 - 已移至Bot级别处理，此方法已废弃
// func (c *Client) handleInteraction(s *discordgo.Session, i *discordgo.InteractionCreate) {
//	// 交互处理已统一到Bot级别，避免重复处理导致的"already acknowledged"错误
// }

// handleSlashCommand 处理斜杠命令 - 已移至Bot级别处理，此方法已废弃
// func (c *Client) handleSlashCommand(ctx context.Context, s *discordgo.Session, i *discordgo.InteractionCreate) {
//	// 斜杠命令处理已统一到Bot级别的CommandManager，避免重复处理
// }

// handleComponent 处理组件交互
func (c *Client) handleComponent(ctx context.Context, s *discordgo.Session, i *discordgo.InteractionCreate) {
	customID := i.MessageComponentData().CustomID

	c.componentMu.RLock()
	component, exists := c.components[customID]
	c.componentMu.RUnlock()

	if !exists {
		logger.Warn("未知组件", "custom_id", customID, "user", c.getUserID(i))
		c.respondWithError(s, i, "未知组件")
		return
	}

	// 执行中间件链
	c.executeWithMiddleware(ctx, func(ctx context.Context) error {
		// 验证组件
		if err := component.Validate(i); err != nil {
			logger.Warn("组件验证失败", "custom_id", customID, "error", err)
			c.respondWithError(s, i, "组件参数无效: "+err.Error())
			return err
		}

		// 执行组件处理
		client := types.NewClient(s, c.config)

		if err := component.Handle(ctx, client, i); err != nil {
			logger.Error("组件处理失败", "custom_id", customID, "user", c.getUserID(i), "error", err)
			c.respondWithError(s, i, "组件处理失败")
			return err
		}

		logger.Debug("组件处理成功", "custom_id", customID, "user", c.getUserID(i))
		return nil
	})
}

// handleModal 处理模态框
func (c *Client) handleModal(ctx context.Context, s *discordgo.Session, i *discordgo.InteractionCreate) {
	customID := i.ModalSubmitData().CustomID

	c.componentMu.RLock()
	modal, exists := c.components[customID]
	c.componentMu.RUnlock()

	if !exists {
		logger.Warn("未知模态框", "custom_id", customID, "user", c.getUserID(i))
		c.respondWithError(s, i, "未知模态框")
		return
	}

	// 执行中间件链
	c.executeWithMiddleware(ctx, func(ctx context.Context) error {
		// 验证模态框
		if err := modal.Validate(i); err != nil {
			logger.Warn("模态框验证失败", "custom_id", customID, "error", err)
			c.respondWithError(s, i, "模态框参数无效: "+err.Error())
			return err
		}

		// 执行模态框处理
		client := types.NewClient(s, c.config)

		if err := modal.Handle(ctx, client, i); err != nil {
			logger.Error("模态框处理失败", "custom_id", customID, "user", c.getUserID(i), "error", err)
			c.respondWithError(s, i, "模态框处理失败")
			return err
		}

		logger.Debug("模态框处理成功", "custom_id", customID, "user", c.getUserID(i))
		return nil
	})
}

// handleAutocomplete 处理自动完成
func (c *Client) handleAutocomplete(ctx context.Context, s *discordgo.Session, i *discordgo.InteractionCreate) {
	// TODO: 实现自动完成处理
	logger.Debug("收到自动完成请求", "command", i.ApplicationCommandData().Name)
}

// executeWithMiddleware 执行中间件链
func (c *Client) executeWithMiddleware(ctx context.Context, handler func(context.Context) error) {
	c.middlewareMu.RLock()
	middlewares := make([]types.Middleware, len(c.middlewares))
	copy(middlewares, c.middlewares)
	c.middlewareMu.RUnlock()

	// 构建中间件链
	var next func(context.Context) error = handler

	for i := len(middlewares) - 1; i >= 0; i-- {
		middleware := middlewares[i]
		currentNext := next
		next = func(ctx context.Context) error {
			return middleware.Process(ctx, currentNext)
		}
	}

	// 执行中间件链
	if err := next(ctx); err != nil {
		logger.Error("中间件执行失败", "error", err)
	}
}

// getUserID 获取用户ID
func (c *Client) getUserID(i *discordgo.InteractionCreate) string {
	if i.Member != nil && i.Member.User != nil {
		return i.Member.User.ID
	}
	if i.User != nil {
		return i.User.ID
	}
	return "unknown"
}

// respondWithError 响应错误消息
func (c *Client) respondWithError(s *discordgo.Session, i *discordgo.InteractionCreate, message string) {
	err := s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Content: "❌ " + message,
			Flags:   discordgo.MessageFlagsEphemeral,
		},
	})
	if err != nil {
		logger.Error("响应交互失败", "error", err)
	}
}
